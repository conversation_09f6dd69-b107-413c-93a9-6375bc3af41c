/* Enhanced Main Application Styles with Cairo Font */
QMainWindow {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f8f9fa, stop:1 #e9ecef);
    font-family: 'Cairo', 'Segoe UI', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
    font-size: 16px;
}

QWidget {
    font-size: 16px;
    color: #212529;
    font-family: 'Cairo', 'Segoe UI', '<PERSON>homa', Aria<PERSON>, sans-serif;
}

/* Enhanced Buttons with Larger Text */
QPushButton {
    padding: 15px 25px;
    border-radius: 10px;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0d6efd, stop:1 #0b5ed7);
    color: white;
    min-width: 140px;
    min-height: 45px;
    border: none;
    font-weight: 600;
    font-size: 17px;
    font-family: 'Cairo', 'Segoe UI', '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0b5ed7, stop:1 #0a58ca);
    box-shadow: 0 6px 16px rgba(13, 110, 253, 0.4);
    transform: translateY(-2px);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0a58ca, stop:1 #084298);
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.2);
}

QPushButton:disabled {
    background-color: #6c757d;
    color: #adb5bd;
    box-shadow: none;
}

QPushButton[flat="true"] {
    background-color: transparent;
    color: #0d6efd;
    border: 2px solid #0d6efd;
    box-shadow: none;
}

QPushButton[flat="true"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(13, 110, 253, 0.1), stop:1 rgba(13, 110, 253, 0.2));
    border-color: #0b5ed7;
}

/* Enhanced Input Fields with Larger Text */
QLineEdit, QComboBox, QSpinBox, QDateEdit, QTextEdit, QPlainTextEdit {
    padding: 15px 18px;
    border: 2px solid #e1e5eb;
    border-radius: 10px;
    background-color: white;
    selection-background-color: #0d6efd;
    selection-color: white;
    font-size: 17px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    min-height: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 3px solid #0d6efd;
    outline: none;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(images/down-arrow.png);
    width: 12px;
    height: 12px;
}

/* Enhanced Tables */
QTableWidget, QTreeView, QListView {
    border: 2px solid #e1e5eb;
    border-radius: 12px;
    gridline-color: #f1f3f5;
    background-color: white;
    alternate-background-color: #f8f9fa;
    font-size: 17px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

QTableWidget::item, QTreeView::item, QListView::item {
    padding: 10px;
    border: none;
}

QTableWidget::item:selected, QTreeView::item:selected, QListView::item:selected {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
}

QHeaderView::section {
    background-color: #f8f9fa;
    color: #495057;
    padding: 12px;
    border: none;
    border-right: 1px solid #dee2e6;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 15px;
}

QHeaderView::section:first {
    border-top-left-radius: 8px;
}

QHeaderView::section:last {
    border-right: none;
    border-top-right-radius: 8px;
}

/* Tabs */
QTabWidget::pane {
    border: 2px solid #e1e5eb;
    border-radius: 8px;
    background-color: white;
}

QTabBar::tab {
    background-color: #f8f9fa;
    padding: 12px 24px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-size: 15px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: white;
    color: #0d6efd;
    border-bottom: 3px solid #0d6efd;
}

/* Enhanced Menu Bar with Larger Size */
QMenuBar {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    border-bottom: 3px solid #0d6efd;
    padding: 15px 10px;
    font-size: 18px;
    font-weight: 600;
    min-height: 50px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    color: #212529;
}

QMenuBar::item {
    background-color: transparent;
    padding: 15px 25px;
    border-radius: 8px;
    margin: 3px;
    font-size: 18px;
    font-weight: 600;
}

QMenuBar::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #0d6efd;
    border: 2px solid #0d6efd;
}

QMenuBar::item:pressed {
    background-color: #0d6efd;
    color: white;
}

QMenu {
    background-color: white;
    border: 3px solid #0d6efd;
    border-radius: 12px;
    padding: 12px;
    font-size: 16px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
}

QMenu::item {
    padding: 15px 25px;
    border-radius: 8px;
    margin: 3px;
    font-size: 16px;
    min-height: 20px;
}

QMenu::item:selected {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    color: #0d6efd;
    font-weight: 600;
}

QMenu::separator {
    height: 3px;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #e1e5eb, stop:0.5 #0d6efd, stop:1 #e1e5eb);
    margin: 12px 0;
    border-radius: 2px;
}

/* Group Box */
QGroupBox {
    border: 2px solid #e1e5eb;
    border-radius: 10px;
    margin-top: 1.8em;
    padding-top: 18px;
    background-color: white;
    font-size: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 12px;
    background-color: white;
    color: #495057;
    font-weight: 600;
    font-size: 16px;
}

/* Status Bar */
QStatusBar {
    background-color: #f8f9fa;
    color: #6c757d;
    border-top: 2px solid #dee2e6;
    font-size: 14px;
}

QStatusBar::item { border: none; }

/* Enhanced Labels with Larger Text */
QLabel {
    color: #212529;
    font-size: 17px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    font-weight: 500;
}

QLabel[heading="true"] {
    font-size: 28px;
    font-weight: bold;
    color: #212529;
    padding: 12px 0;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
}

QLabel[subheading="true"] {
    font-size: 20px;
    color: #6c757d;
    padding: 8px 0;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    font-weight: 500;
}

/* Scroll Bars */
QScrollBar:vertical {
    border: none;
    background: #f1f3f5;
    width: 12px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #adb5bd;
    min-height: 30px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background: #6c757d;
}

QScrollBar:horizontal {
    border: none;
    background: #f1f3f5;
    height: 12px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #adb5bd;
    min-width: 30px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background: #6c757d;
}

/* Tool Tips */
QToolTip {
    background-color: #212529;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 6px;
    font-size: 14px;
}

/* Dashboard Cards */
QFrame[card="true"] {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Enhanced Home Screen Buttons */
QPushButton[homeButton="true"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #ffffff, stop:1 #f8f9fa);
    border: 3px solid #e1e5eb;
    border-radius: 20px;
    padding: 35px;
    font-size: 22px;
    min-width: 250px;
    min-height: 160px;
    color: #212529;
    font-weight: bold;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

QPushButton[homeButton="true"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #e3f2fd, stop:1 #bbdefb);
    border: 3px solid #0d6efd;
    color: #0d6efd;
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.2);
    transform: translateY(-3px);
}

/* Modern Scrollbar Styling */
QScrollBar:vertical {
    background: #f8f9fa;
    width: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0d6efd, stop:1 #0b5ed7);
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0b5ed7, stop:1 #0a58ca);
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0;
}

QScrollBar:horizontal {
    background: #f8f9fa;
    height: 12px;
    border-radius: 6px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0d6efd, stop:1 #0b5ed7);
    border-radius: 6px;
    min-width: 20px;
}

QScrollBar::handle:horizontal:hover {
    background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #0b5ed7, stop:1 #0a58ca);
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0;
}
