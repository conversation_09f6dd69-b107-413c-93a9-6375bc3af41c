/* Main Application Styles */
QMainWindow {
    background-color: #f1f3f9;
    font-family: 'Segoe UI', Arial, sans-serif;
}

QWidget {
    font-size: 14px;
    color: #333;
}

/* Buttons */
QPushButton {
    padding: 10px 20px;
    border-radius: 8px;
    background-color: #0d6efd;
    color: white;
    min-width: 120px;
    border: none;
    font-weight: 500;
    font-size: 15px;
}

QPushButton:hover {
    background-color: #0b5ed7;
}

QPushButton:pressed {
    background-color: #0a58ca;
}

QPushButton:disabled {
    background-color: #6c757d;
    color: #adb5bd;
}

QPushButton[flat="true"] {
    background-color: transparent;
    color: #0d6efd;
    border: 1px solid #0d6efd;
}

QPushButton[flat="true"]:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* Input Fields */
QLineEdit, QComboBox, QSpinBox, QDateEdit, QTextEdit, QPlainTextEdit {
    padding: 12px;
    border: 2px solid #e1e5eb;
    border-radius: 8px;
    background-color: white;
    selection-background-color: #0d6efd;
    selection-color: white;
    font-size: 15px;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #0d6efd;
    outline: none;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(images/down-arrow.png);
    width: 12px;
    height: 12px;
}

/* Tables */
QTableWidget, QTreeView, QListView {
    border: 2px solid #e1e5eb;
    border-radius: 8px;
    gridline-color: #f1f3f5;
    background-color: white;
    alternate-background-color: #f8f9fa;
    font-size: 14px;
}

QTableWidget::item, QTreeView::item, QListView::item {
    padding: 10px;
    border: none;
}

QTableWidget::item:selected, QTreeView::item:selected, QListView::item:selected {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
}

QHeaderView::section {
    background-color: #f8f9fa;
    color: #495057;
    padding: 12px;
    border: none;
    border-right: 1px solid #dee2e6;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    font-size: 15px;
}

QHeaderView::section:first {
    border-top-left-radius: 8px;
}

QHeaderView::section:last {
    border-right: none;
    border-top-right-radius: 8px;
}

/* Tabs */
QTabWidget::pane {
    border: 2px solid #e1e5eb;
    border-radius: 8px;
    background-color: white;
}

QTabBar::tab {
    background-color: #f8f9fa;
    padding: 12px 24px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    font-size: 15px;
    font-weight: 500;
}

QTabBar::tab:selected {
    background-color: white;
    color: #0d6efd;
    border-bottom: 3px solid #0d6efd;
}

/* Menu Bar */
QMenuBar {
    background-color: #343a40;
    color: white;
    border-bottom: 2px solid #212529;
    font-size: 15px;
}

QMenuBar::item {
    padding: 10px 18px;
    border-radius: 6px;
}

QMenuBar::item:selected {
    background-color: #495057;
}

QMenu {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 8px 0;
    font-size: 14px;
}

QMenu::item {
    padding: 10px 28px;
}

QMenu::item:selected {
    background-color: #f8f9fa;
    color: #0d6efd;
}

/* Group Box */
QGroupBox {
    border: 2px solid #e1e5eb;
    border-radius: 10px;
    margin-top: 1.8em;
    padding-top: 18px;
    background-color: white;
    font-size: 15px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 12px;
    background-color: white;
    color: #495057;
    font-weight: 600;
    font-size: 16px;
}

/* Status Bar */
QStatusBar {
    background-color: #f8f9fa;
    color: #6c757d;
    border-top: 2px solid #dee2e6;
    font-size: 14px;
}

QStatusBar::item { border: none; }

/* Labels */
QLabel {
    color: #212529;
    font-size: 14px;
}

QLabel[heading="true"] {
    font-size: 22px;
    font-weight: bold;
    color: #212529;
    padding: 8px 0;
}

QLabel[subheading="true"] {
    font-size: 16px;
    color: #6c757d;
    padding: 5px 0;
}

/* Scroll Bars */
QScrollBar:vertical {
    border: none;
    background: #f1f3f5;
    width: 12px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #adb5bd;
    min-height: 30px;
    border-radius: 6px;
}

QScrollBar::handle:vertical:hover {
    background: #6c757d;
}

QScrollBar:horizontal {
    border: none;
    background: #f1f3f5;
    height: 12px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #adb5bd;
    min-width: 30px;
    border-radius: 6px;
}

QScrollBar::handle:horizontal:hover {
    background: #6c757d;
}

/* Tool Tips */
QToolTip {
    background-color: #212529;
    color: white;
    border: none;
    padding: 8px;
    border-radius: 6px;
    font-size: 14px;
}

/* Dashboard Cards */
QFrame[card="true"] {
    background-color: white;
    border-radius: 12px;
    padding: 20px;
    margin: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* Home Screen Buttons */
QPushButton[homeButton="true"] {
    background-color: white;
    border: 2px solid #e1e5eb;
    border-radius: 16px;
    padding: 30px;
    font-size: 20px;
    min-width: 220px;
    min-height: 140px;
    color: #212529;
    font-weight: bold;
}

QPushButton[homeButton="true"]:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd;
    color: #0d6efd;
}
