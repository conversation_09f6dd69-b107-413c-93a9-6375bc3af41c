/* Enhanced Login Dialog Styles with Cairo Font */
QDialog {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #f1f3f9, stop:1 #e9ecef);
    font-family: 'Cairo', 'Segoe UI', '<PERSON><PERSON><PERSON>', Arial, sans-serif;
}

QLineEdit {
    padding: 18px 20px;
    border: 3px solid #e1e5eb;
    border-radius: 12px;
    min-width: 380px;
    font-size: 18px;
    background-color: white;
    color: #212529;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    min-height: 25px;
}

QLineEdit:focus {
    border: 3px solid #0d6efd;
    outline: none;
    box-shadow: 0 6px 16px rgba(13, 110, 253, 0.3);
}

QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0d6efd, stop:1 #0b5ed7);
    color: white;
    border: none;
    padding: 18px 35px;
    border-radius: 12px;
    min-width: 180px;
    font-size: 20px;
    font-weight: bold;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 6px 16px rgba(13, 110, 253, 0.3);
    min-height: 50px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0b5ed7, stop:1 #0a58ca);
    box-shadow: 0 8px 20px rgba(13, 110, 253, 0.4);
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0a58ca, stop:1 #084298);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
}

QLabel {
    color: #212529;
    font-size: 18px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    font-weight: 500;
}

QFormLayout {
    spacing: 25px;
}

/* Enhanced Logo Styles */
QLabel#logoLabel {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #0d6efd, stop:1 #0b5ed7);
    color: white;
    border-radius: 70px;
    min-width: 140px;
    max-width: 140px;
    min-height: 140px;
    max-height: 140px;
    font-size: 70px;
    font-weight: bold;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

QLabel#titleLabel {
    font-size: 38px;
    font-weight: bold;
    color: #212529;
    margin-top: 20px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
}

QLabel#subtitleLabel {
    font-size: 24px;
    color: #6c757d;
    margin-bottom: 35px;
    font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
    font-weight: 500;
}

/* Main widget styling */
QWidget#mainWidget {
    background-color: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Login button styling */
QPushButton#loginButton {
    background-color: #0d6efd;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    padding: 15px;
    min-height: 55px;
}

QPushButton#loginButton:hover {
    background-color: #0b5ed7;
}

QPushButton#loginButton:pressed {
    background-color: #0a58ca;
}