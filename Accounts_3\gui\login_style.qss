/* Login Dialog Styles */
QDialog {
    background-color: #f1f3f9;
    font-family: 'Segoe UI', Arial, sans-serif;
}

QLineEdit {
    padding: 15px;
    border: 2px solid #e1e5eb;
    border-radius: 10px;
    min-width: 350px;
    font-size: 16px;
    background-color: white;
    color: #333;
}

QLineEdit:focus {
    border: 2px solid #0d6efd;
    outline: none;
}

QPushButton {
    background-color: #0d6efd;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 10px;
    min-width: 150px;
    font-size: 18px;
    font-weight: bold;
}

QPushButton:hover {
    background-color: #0b5ed7;
}

QPushButton:pressed {
    background-color: #0a58ca;
}

QLabel {
    color: #212529;
    font-size: 16px;
}

QFormLayout {
    spacing: 25px;
}

/* Logo Styles */
QLabel#logoLabel {
    background-color: #0d6efd;
    color: white;
    border-radius: 60px;
    min-width: 120px;
    max-width: 120px;
    min-height: 120px;
    max-height: 120px;
    font-size: 60px;
    font-weight: bold;
}

QLabel#titleLabel {
    font-size: 32px;
    font-weight: bold;
    color: #212529;
    margin-top: 15px;
}

QLabel#subtitleLabel {
    font-size: 20px;
    color: #6c757d;
    margin-bottom: 30px;
}

/* Main widget styling */
QWidget#mainWidget {
    background-color: white;
    border-radius: 16px;
    padding: 30px;
    margin: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* Login button styling */
QPushButton#loginButton {
    background-color: #0d6efd;
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    padding: 15px;
    min-height: 55px;
}

QPushButton#loginButton:hover {
    background-color: #0b5ed7;
}

QPushButton#loginButton:pressed {
    background-color: #0a58ca;
}