from PyQt5.QtWidgets import (QMain<PERSON><PERSON>ow, QWidget, QVBoxLayout, QHBoxLayout, 
                         QPushButton, QLabel, QFrame, QGridLayout, QApplication,
                         QMenuBar, QMenu, QAction, QDialog, QMessageBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon, QFont, QPixmap
import sys
import os
from .sales import SalesWidget
from .purchases import PurchasesWidget
from .inventory import InventoryWidget
from .contacts import ContactsWidget
from .reports import ReportsWidget
from .finance import FinanceWidget
from .accounting import AccountingWidget
from .backup import BackupDialog
from .dashboard import DashboardWidget
from .notifications import NotificationSystem
from .advanced_reports import AdvancedAnalyticsWidget  # تصحيح اسم الكلاس المستورد
from .login import LoginDialog
from .audit_log import AuditLogDialog
from database.audit import AuditSystem, audit_action

class MainWindow(QMainWindow):
    def __init__(self, engine=None):
        super().__init__()
        self.engine = engine
        self.current_user = None
        
        if not self.show_login():
            sys.exit()
            
        # إضافة نظام التدقيق
        self.audit_system = AuditSystem(engine)
        
        self.setWindowTitle(f"Elashrafy - نظام المحاسبة المتطور - {self.current_user.full_name}")
        self.setMinimumSize(1200, 800)
        self.setLayoutDirection(Qt.RightToLeft)
        
        # إعداد نظام التنبيهات
        if engine:
            self.notification_system = NotificationSystem(engine)
            
        self.setup_menu()
        self.show_dashboard()

    @audit_action("تسجيل دخول")
    def show_login(self):
        """عرض نافذة تسجيل الدخول مع تسجيل العملية"""
        if self.engine:
            login_dialog = LoginDialog(self.engine)
            if login_dialog.exec_() == QDialog.Accepted:
                self.current_user = login_dialog.user
                return True
        return False

    def check_permission(self, permission_name):
        """التحقق من صلاحيات المستخدم"""
        if not self.current_user:
            return False
            
        for role in self.current_user.roles:
            for permission in role.permissions:
                if permission.name == permission_name:
                    return True
        return False

    def setup_menu(self):
        # تحديث تصميم شريط القوائم
        menubar = self.menuBar()
        menubar.setLayoutDirection(Qt.RightToLeft)
        menubar.setStyleSheet("""
            QMenuBar {
                background-color: #2C3E50;
                color: white;
                border-bottom: 1px solid #34495E;
                padding: 8px;
                font-size: 14px;
            }
            QMenuBar::item {
                padding: 8px 15px;
                margin-left: 5px;
                border-radius: 4px;
            }
            QMenuBar::item:selected {
                background-color: #34495E;
            }
            QMenu {
                background-color: #2C3E50;
                color: white;
                border: 1px solid #34495E;
                padding: 5px;
            }
            QMenu::item {
                padding: 8px 25px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #34495E;
            }
        """)

        # إضافة الأيقونات للقوائم
        home_action = QAction(QIcon("images/home.png"), "الصفحة الرئيسية", self)
        home_action.triggered.connect(self.show_home)
        menubar.addAction(home_action)

        file_menu = menubar.addMenu("ملف")
        backup_action = QAction(QIcon("images/backup.png"), "النسخ الاحتياطي", self)
        backup_action.triggered.connect(self.show_backup_dialog)
        exit_action = QAction(QIcon("images/exit.png"), "خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addActions([backup_action, exit_action])

        # قائمة المبيعات
        if self.check_permission("إدارة_المبيعات"):
            sales_menu = menubar.addMenu("المبيعات")
            new_sale = QAction("فاتورة مبيعات جديدة", self)
            new_sale.triggered.connect(self.show_sales)
            sales_menu.addAction(new_sale)

        # قائمة المشتريات
        if self.check_permission("إدارة_المشتريات"):
            purchases_menu = menubar.addMenu("المشتريات")
            new_purchase = QAction("فاتورة مشتريات جديدة", self)
            new_purchase.triggered.connect(self.show_purchases)
            purchases_menu.addAction(new_purchase)

        # قائمة العملاء والموردين
        if self.check_permission("إدارة_العملاء"):
            contacts_menu = menubar.addMenu("العملاء والموردين")
            customers = QAction("كشف حساب عميل", self)
            customers.triggered.connect(self.show_customer_statement)
            suppliers = QAction("كشف حساب مورد", self)
            suppliers.triggered.connect(self.show_supplier_statement)
            contacts_menu.addActions([customers, suppliers])

        # قائمة المخزون
        if self.check_permission("إدارة_المخزون"):
            inventory_menu = menubar.addMenu("المخزون")
            stock_action = QAction("جرد المخزون", self)
            stock_action.triggered.connect(self.show_inventory)
            inventory_menu.addAction(stock_action)

        # قائمة الحسابات
        accounting_menu = menubar.addMenu("الحسابات")
        income_statement = QAction("قائمة الدخل", self)
        balance_sheet = QAction("الميزانية العمومية", self)
        general_ledger = QAction("دفتر الأستاذ العام", self)
        income_statement.triggered.connect(lambda: self.show_financial_report("قائمة الدخل"))
        balance_sheet.triggered.connect(lambda: self.show_financial_report("الميزانية العمومية"))
        general_ledger.triggered.connect(lambda: self.show_financial_report("دفتر الأستاذ العام"))
        accounting_menu.addActions([income_statement, balance_sheet, general_ledger])
        
        # إضافة قائمة التقارير المتقدمة
        reports_menu = menubar.addMenu("التقارير المتقدمة")
        advanced_reports = QAction("التقارير التحليلية", self)
        advanced_reports.triggered.connect(self.show_advanced_reports)
        reports_menu.addAction(advanced_reports)
        
        # إضافة قائمة التنبيهات
        notifications_menu = menubar.addMenu("التنبيهات")
        show_notifications = QAction("عرض التنبيهات", self)
        show_notifications.triggered.connect(self.show_notifications_window)
        notifications_settings = QAction("إعدادات التنبيهات", self)
        notifications_settings.triggered.connect(self.show_notifications_settings)
        notifications_menu.addActions([show_notifications, notifications_settings])

        # قائمة النظام
        system_menu = menubar.addMenu("النظام")
        
        if self.check_permission("إدارة_المستخدمين"):
            users_action = QAction("إدارة المستخدمين", self)
            users_action.triggered.connect(self.show_user_management)
            system_menu.addAction(users_action)
            
        if self.check_permission("النسخ_الاحتياطي"):
            backup_action = QAction("النسخ الاحتياطي", self)
            backup_action.triggered.connect(self.show_backup_dialog)
            system_menu.addAction(backup_action)
            
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        system_menu.addAction(logout_action)
        
        # إضافة خيار سجل العمليات في قائمة النظام
        audit_log_action = QAction("سجل العمليات", self)
        audit_log_action.triggered.connect(self.show_audit_log)
        system_menu.addAction(audit_log_action)

    @audit_action("تسجيل خروج")
    def logout(self):
        """تسجيل الخروج مع تسجيل العملية"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل تريد تسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.close()
            QApplication.instance().quit()

    def show_user_management(self):
        from .login import UserManagementDialog
        dialog = UserManagementDialog(self.engine)
        dialog.exec_()

    def show_home(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(25, 25, 25, 25)
        main_layout.setSpacing(30)

        # إضافة لوحة المعلومات في الأعلى
        dashboard = DashboardWidget(self.engine)
        main_layout.addWidget(dashboard)

        # إضافة الأزرار الرئيسية في الأسفل
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttonsFrame")
        buttons_frame.setStyleSheet("""
            #buttonsFrame {
                background-color: #F8F9FA;
                border-radius: 15px;
                padding: 30px;
            }
            QPushButton {
                background-color: white;
                border: 2px solid #DEE2E6;
                border-radius: 15px;
                padding: 30px;
                font-size: 18px;
                min-width: 200px;
                min-height: 120px;
            }
            QPushButton:hover {
                background-color: #E9ECEF;
                border-color: #0D6EFD;
                color: #0D6EFD;
            }
        """)
        
        grid_layout = QGridLayout(buttons_frame)
        grid_layout.setSpacing(25)
        grid_layout.setContentsMargins(20, 20, 20, 20)

        # الأزرار الرئيسية
        menu_buttons = [
            ("المبيعات", "فواتير المبيعات وإدارة العملاء", self.show_sales),
            ("المشتريات", "فواتير المشتريات وإدارة الموردين", self.show_purchases),
            ("المخزون", "إدارة المنتجات والمخزون", self.show_inventory),
            ("العملاء والموردين", "كشوفات الحسابات والأرصدة", lambda: self.show_customer_statement()),
            ("التقارير المالية", "التقارير والتحليلات المالية", self.show_financial_reports),
            ("النسخ الاحتياطي", "حفظ واسترجاع البيانات", self.show_backup_dialog)
        ]

        for index, (title, subtitle, callback) in enumerate(menu_buttons):
            btn = QPushButton()
            btn.setProperty("homeButton", True)
            btn.clicked.connect(callback)

            btn_layout = QVBoxLayout()
            btn_layout.setAlignment(Qt.AlignCenter)
            btn_layout.setContentsMargins(20, 20, 20, 20)
            btn_layout.setSpacing(15)

            title_label = QLabel(title)
            title_label.setStyleSheet("""
                font-size: 22px;
                font-weight: bold;
                color: #212529;
                font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
                margin-bottom: 8px;
            """)
            title_label.setAlignment(Qt.AlignCenter)

            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("""
                font-size: 16px;
                color: #6C757D;
                font-family: 'Cairo', 'Segoe UI', 'Tahoma', Arial, sans-serif;
                font-weight: 500;
                line-height: 1.4;
            """)
            subtitle_label.setAlignment(Qt.AlignCenter)
            subtitle_label.setWordWrap(True)

            btn_layout.addWidget(title_label)
            btn_layout.addWidget(subtitle_label)
            btn.setLayout(btn_layout)

            grid_layout.addWidget(btn, index // 3, index % 3)

        main_layout.addWidget(buttons_frame)

    def show_financial_report(self, report_type):
        accounting_widget = AccountingWidget(self.engine)
        accounting_widget.report_type.setCurrentText(report_type)
        self.setCentralWidget(accounting_widget)

    def show_dashboard(self):
        if self.engine:
            dashboard = DashboardWidget(self.engine)
            self.setCentralWidget(dashboard)
            
    def show_advanced_reports(self):
        if self.engine:
            self.setCentralWidget(AdvancedReportsWidget(self.engine))

    def show_notifications_window(self):
        if hasattr(self, 'notification_system'):
            self.notification_system.show_notifications()

    def show_notifications_settings(self):
        if hasattr(self, 'notification_system'):
            self.notification_system.show_settings()

    @audit_action("عرض المبيعات")
    def show_sales(self, *args):
        """عرض واجهة المبيعات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المبيعات"):
                self.setCentralWidget(SalesWidget(self.engine))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المبيعات: {str(e)}")
            print(f"Error in show_sales: {e}")

    @audit_action("عرض المشتريات")
    def show_purchases(self, *args):
        """عرض واجهة المشتريات مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المشتريات"):
                self.setCentralWidget(PurchasesWidget(self.engine))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المشتريات: {str(e)}")
            print(f"Error in show_purchases: {e}")

    def show_customer_statement(self):
        if self.engine:
            contacts_widget = ContactsWidget(self.engine)
            contacts_widget.tab_widget.setCurrentIndex(0)  # تبويب العملاء
            self.setCentralWidget(contacts_widget)

    def show_supplier_statement(self):
        if self.engine:
            contacts_widget = ContactsWidget(self.engine)
            contacts_widget.tab_widget.setCurrentIndex(1)  # تبويب الموردين
            self.setCentralWidget(contacts_widget)

    def show_financial_reports(self):
        if self.engine:
            self.setCentralWidget(AccountingWidget(self.engine))

    @audit_action("عرض المخزون")
    def show_inventory(self, *args):
        """عرض واجهة المخزون مع تسجيل العملية"""
        try:
            if self.check_permission("إدارة_المخزون"):
                self.setCentralWidget(InventoryWidget(self.engine))
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض المخزون: {str(e)}")
            print(f"Error in show_inventory: {e}")

    def show_backup_dialog(self):
        if self.engine:
            dialog = BackupDialog(os.path.join(os.path.dirname(__file__), '..', 'accounting.db'))
            dialog.exec_()
            
    def show_audit_log(self):
        """عرض نافذة سجل العمليات"""
        if self.check_permission("إدارة_المستخدمين"):
            dialog = AuditLogDialog(self.engine)
            dialog.exec_()
        else:
            QMessageBox.warning(self, "تنبيه", "لا تملك صلاحية الوصول لسجل العمليات")

    def closeEvent(self, event):
        """معالجة إغلاق التطبيق"""
        # حفظ الإعدادات وإغلاق نظام التنبيهات
        if hasattr(self, 'notification_system'):
            self.notification_system.quit_app()
        event.accept()

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())