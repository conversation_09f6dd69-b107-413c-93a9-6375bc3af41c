from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QDateEdit, QTabWidget, QFrame, QGridLayout, QHeaderView)
from PyQt5.QtCore import Qt, QDate, QTimer
from PyQt5.QtGui import QColor, QFont
from sqlalchemy.orm import Session
from sqlalchemy import or_
from database.models import (Transaction, TransactionType, ExpenseCategory, 
                           IncomeCategory)
import datetime

class CategoryDialog(QDialog):
    def __init__(self, engine, is_expense=True):
        super().__init__()
        self.engine = engine
        self.is_expense = is_expense
        self.setWindowTitle(f"تصنيف {'مصروفات' if is_expense else 'إيرادات'} جديد")
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # نموذج إضافة تصنيف
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        form_layout = QGridLayout()
        form_frame.setLayout(form_layout)
        
        self.name_input = QLineEdit()
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 250px;
            }
        """)
        self.description_input = QLineEdit()
        self.description_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 250px;
            }
        """)
        
        form_layout.addWidget(QLabel("اسم التصنيف:"), 0, 0)
        form_layout.addWidget(self.name_input, 0, 1)
        form_layout.addWidget(QLabel("الوصف:"), 1, 0)
        form_layout.addWidget(self.description_input, 1, 1)
        
        layout.addWidget(form_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        save_btn = QPushButton("حفظ")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        save_btn.clicked.connect(self.save_category)
        cancel_btn.clicked.connect(self.reject)
        
    def save_category(self):
        name = self.name_input.text()
        if not name:
            QMessageBox.warning(self, "خطأ", "يجب إدخال اسم التصنيف")
            return
            
        try:
            with Session(self.engine) as session:
                category = ExpenseCategory if self.is_expense else IncomeCategory
                new_category = category(
                    name=name,
                    description=self.description_input.text()
                )
                session.add(new_category)
                session.commit()
                self.accept()
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ التصنيف: {str(e)}")

class FinanceTab(QWidget):
    def __init__(self, engine, is_expense=True):
        super().__init__()
        self.engine = engine
        self.is_expense = is_expense
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # منطقة إضافة معاملة جديدة
        form_frame = QFrame()
        form_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 15px;
            }
        """)
        form_layout = QGridLayout()
        form_frame.setLayout(form_layout)
        
        # حقول النموذج
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 200px;
            }
        """)
        add_category_btn = QPushButton("+")
        add_category_btn.setFixedWidth(30)
        add_category_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                border-radius: 15px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        
        self.amount = QDoubleSpinBox()
        self.amount.setMaximum(1000000)
        self.amount.setStyleSheet("""
            QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """)
        
        self.date = QDateEdit()
        self.date.setDate(QDate.currentDate())
        self.date.setStyleSheet("""
            QDateEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 150px;
            }
        """)
        
        self.notes = QLineEdit()
        self.notes.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
            }
        """)
        
        # إضافة الحقول للنموذج
        form_layout.addWidget(QLabel("التصنيف:"), 0, 0)
        category_layout = QHBoxLayout()
        category_layout.addWidget(self.category_combo)
        category_layout.addWidget(add_category_btn)
        form_layout.addLayout(category_layout, 0, 1)
        
        form_layout.addWidget(QLabel("المبلغ:"), 0, 2)
        form_layout.addWidget(self.amount, 0, 3)
        
        form_layout.addWidget(QLabel("التاريخ:"), 1, 0)
        form_layout.addWidget(self.date, 1, 1)
        
        form_layout.addWidget(QLabel("ملاحظات:"), 1, 2)
        form_layout.addWidget(self.notes, 1, 3)
        
        # زر الإضافة
        add_btn = QPushButton("إضافة معاملة")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #0056B3;
            }
        """)
        form_layout.addWidget(add_btn, 2, 3)
        
        layout.addWidget(form_frame)
        
        # جدول المعاملات
        self.transactions_table = QTableWidget()
        self.transactions_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
            }
        """)
        self.transactions_table.setColumnCount(6)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "التصنيف", "المبلغ", "ملاحظات", "المستخدم", "حذف"
        ])
        
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.Stretch)
        for i in [0, 2, 4, 5]:
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            header.resizeSection(i, 120)
            
        layout.addWidget(self.transactions_table)
        
        # ربط الأحداث
        add_category_btn.clicked.connect(self.add_category)
        add_btn.clicked.connect(self.add_transaction)
        
        # تحميل التصنيفات
        self.refresh_categories()
        # تحميل المعاملات
        self.refresh_transactions()
        
    def refresh_categories(self):
        with Session(self.engine) as session:
            categories = session.query(
                ExpenseCategory if self.is_expense else IncomeCategory
            ).all()
            
            self.category_combo.clear()
            for category in categories:
                self.category_combo.addItem(category.name, category.id)
                
    def add_category(self):
        dialog = CategoryDialog(self.engine, self.is_expense)
        if dialog.exec_() == QDialog.Accepted:
            self.refresh_categories()
            
    def add_transaction(self):
        if not self.category_combo.currentData():
            QMessageBox.warning(self, "خطأ", "يجب اختيار التصنيف")
            return
            
        if self.amount.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يجب إدخال مبلغ أكبر من صفر")
            return
            
        try:
            with Session(self.engine) as session:
                transaction = Transaction(
                    type=TransactionType.EXPENSE if self.is_expense else TransactionType.INCOME,
                    total_amount=self.amount.value(),
                    paid_amount=self.amount.value(),
                    date=self.date.date().toPyDate(),
                    notes=self.notes.text(),
                    expense_category_id=self.category_combo.currentData() if self.is_expense else None,
                    income_category_id=self.category_combo.currentData() if not self.is_expense else None
                )
                session.add(transaction)
                session.commit()
                
            self.refresh_transactions()
            self.clear_inputs()
            QMessageBox.information(self, "نجاح", "تم إضافة المعاملة بنجاح")
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ المعاملة: {str(e)}")
            
    def refresh_transactions(self):
        with Session(self.engine) as session:
            transactions = session.query(Transaction).filter(
                Transaction.type == (TransactionType.EXPENSE if self.is_expense else TransactionType.INCOME)
            ).order_by(Transaction.date.desc()).all()
            
            self.transactions_table.setRowCount(len(transactions))
            
            for row, trans in enumerate(transactions):
                bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")
                
                self.transactions_table.setItem(row, 0, 
                    self.create_table_item(trans.date.strftime("%Y-%m-%d"), bg_color))
                    
                category = trans.expense_category if self.is_expense else trans.income_category
                self.transactions_table.setItem(row, 1,
                    self.create_table_item(category.name if category else "", bg_color))
                    
                amount_item = self.create_table_item(f"{trans.total_amount:,.2f}", bg_color)
                amount_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                self.transactions_table.setItem(row, 2, amount_item)
                
                self.transactions_table.setItem(row, 3,
                    self.create_table_item(trans.notes or "", bg_color))
                    
                self.transactions_table.setItem(row, 4,
                    self.create_table_item("النظام", bg_color))
                    
                delete_btn = QPushButton("حذف")
                delete_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #DC3545;
                        border: none;
                        color: white;
                        padding: 5px 10px;
                    }
                    QPushButton:hover {
                        background-color: #C82333;
                    }
                """)
                delete_btn.clicked.connect(lambda checked, t=trans: self.delete_transaction(t.id))
                self.transactions_table.setCellWidget(row, 5, delete_btn)
                
    def create_table_item(self, text, bg_color, alignment=Qt.AlignCenter):
        item = QTableWidgetItem(str(text))
        item.setBackground(bg_color)
        item.setTextAlignment(alignment)
        return item
                
    def delete_transaction(self, transaction_id):
        reply = QMessageBox.question(self, "تأكيد", 
            "هل أنت متأكد من حذف هذه المعاملة؟",
            QMessageBox.Yes | QMessageBox.No)
            
        if reply == QMessageBox.Yes:
            try:
                with Session(self.engine) as session:
                    transaction = session.query(Transaction).get(transaction_id)
                    if transaction:
                        session.delete(transaction)
                        session.commit()
                        self.refresh_transactions()
                        
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المعاملة: {str(e)}")
                
    def clear_inputs(self):
        self.amount.setValue(0)
        self.date.setDate(QDate.currentDate())
        self.notes.clear()

class FinanceWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # إنشاء تبويبات للمصروفات والإيرادات
        tabs = QTabWidget()
        tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background: white;
            }
            QTabBar::tab {
                background: #F8F9FA;
                border: 1px solid #DEE2E6;
                padding: 8px 30px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background: white;
                border-bottom-color: white;
            }
        """)
        tabs.addTab(FinanceTab(self.engine, True), "المصروفات")
        tabs.addTab(FinanceTab(self.engine, False), "الإيرادات")
        layout.addWidget(tabs)