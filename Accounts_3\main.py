from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import Qt
import sys
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from database.users import init_db
from gui.main_window import MainWindow

def main():
    try:
        app = QApplication(sys.argv)
        app.setLayoutDirection(Qt.RightToLeft)
        
        # تهيئة قاعدة البيانات
        db_path = os.path.join(os.path.dirname(__file__), 'accounting.db')
        engine = create_engine(f'sqlite:///{db_path}', echo=True)
        
        # إنشاء جلسة قاعدة البيانات
        Session = scoped_session(sessionmaker(bind=engine))
        
        # إنشاء قاعدة البيانات إذا لم تكن موجودة
        if not os.path.exists(db_path):
            init_db(engine)
        
        # تطبيق الثيم العربي
        with open(os.path.join(os.path.dirname(__file__), 'gui', 'main_style.qss'), 'r', encoding='utf-8') as style_file:
            app.setStyleSheet(style_file.read())
        
        # إنشاء النافذة الرئيسية
        window = MainWindow(engine=engine)
        window.show()
        
        return app.exec_()
    except Exception as e:
        QMessageBox.critical(None, "خطأ", f"حدث خطأ أثناء تشغيل البرنامج: {str(e)}")
        print(f"Error starting application: {e}")
        return 1
    finally:
        if 'Session' in locals():
            Session.remove()

if __name__ == "__main__":
    sys.exit(main())