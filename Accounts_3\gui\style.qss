/* Global Styles */
QWidget {
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 13px;
    color: #212529;
}

QDialog, QMainWindow {
    background-color: #f8f9fa;
}

/* Buttons */
QPushButton {
    padding: 8px 16px;
    border-radius: 6px;
    background-color: #0d6efd;
    color: white;
    min-width: 100px;
    border: none;
    font-weight: 500;
}

QPushButton:hover {
    background-color: #0b5ed7;
}

QPushButton:pressed {
    background-color: #0a58ca;
}

QPushButton:disabled {
    background-color: #6c757d;
    color: #adb5bd;
}

QPushButton[flat="true"] {
    background-color: transparent;
    color: #0d6efd;
    border: 1px solid #0d6efd;
}

QPushButton[flat="true"]:hover {
    background-color: rgba(13, 110, 253, 0.1);
}

/* Input Fields */
QLineEdit, QComboBox, QSpinBox, QDateEdit, QTextEdit, QPlainTextEdit {
    padding: 10px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background-color: white;
    selection-background-color: #0d6efd;
    selection-color: white;
}

QLineEdit:focus, QComboBox:focus, QSpinBox:focus, QDateEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border: 2px solid #0d6efd;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(images/down-arrow.png);
    width: 12px;
    height: 12px;
}

/* Tables */
QTableWidget, QTreeView, QListView {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    gridline-color: #f1f3f5;
    background-color: white;
    alternate-background-color: #f8f9fa;
}

QTableWidget::item, QTreeView::item, QListView::item {
    padding: 8px;
    border: none;
}

QTableWidget::item:selected, QTreeView::item:selected, QListView::item:selected {
    background-color: rgba(13, 110, 253, 0.2);
    color: #0d6efd;
}

QHeaderView::section {
    background-color: #f8f9fa;
    color: #495057;
    padding: 10px;
    border: none;
    border-right: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

QHeaderView::section:first {
    border-top-left-radius: 6px;
}

QHeaderView::section:last {
    border-right: none;
    border-top-right-radius: 6px;
}

/* Tabs */
QTabWidget::pane {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: white;
}

QTabBar::tab {
    background-color: #f8f9fa;
    padding: 10px 20px;
    margin-right: 2px;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

QTabBar::tab:selected {
    background-color: white;
    color: #0d6efd;
    border-bottom: 3px solid #0d6efd;
}

/* Menu Bar */
QMenuBar {
    background-color: #343a40;
    color: white;
    border-bottom: 1px solid #212529;
}

QMenuBar::item {
    padding: 8px 15px;
    border-radius: 4px;
}

QMenuBar::item:selected {
    background-color: #495057;
}

QMenu {
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 5px 0;
}

QMenu::item {
    padding: 8px 25px;
}

QMenu::item:selected {
    background-color: #f8f9fa;
    color: #0d6efd;
}

/* Group Box */
QGroupBox {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-top: 1.5em;
    padding-top: 15px;
    background-color: white;
}

QGroupBox::title {
    subcontrol-origin: margin;
    subcontrol-position: top center;
    padding: 0 10px;
    background-color: white;
    color: #495057;
    font-weight: 600;
}

/* Status Bar */
QStatusBar {
    background-color: #f8f9fa;
    color: #6c757d;
    border-top: 1px solid #dee2e6;
}

QStatusBar::item { border: none; }

/* Labels */
QLabel {
    color: #212529;
}

QLabel[heading="true"] {
    font-size: 18px;
    font-weight: bold;
    color: #212529;
    padding: 5px 0;
}

QLabel[subheading="true"] {
    font-size: 14px;
    color: #6c757d;
    padding: 3px 0;
}

/* Scroll Bars */
QScrollBar:vertical {
    border: none;
    background: #f1f3f5;
    width: 10px;
    margin: 0;
}

QScrollBar::handle:vertical {
    background: #adb5bd;
    min-height: 30px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background: #6c757d;
}

QScrollBar:horizontal {
    border: none;
    background: #f1f3f5;
    height: 10px;
    margin: 0;
}

QScrollBar::handle:horizontal {
    background: #adb5bd;
    min-width: 30px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background: #6c757d;
}

/* Tool Tips */
QToolTip {
    background-color: #212529;
    color: white;
    border: none;
    padding: 5px;
    border-radius: 4px;
}