from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QPushButton, QLineEdit, QComboBox, QLabel, QSpinBox,
                             QDoubleSpinBox, QTableWidgetItem, QMessageBox, QDialog,
                             QFrame, QGridLayout, QHeaderView, QDateEdit)
from PyQt5.QtCore import Qt, QDate
from PyQt5.QtGui import QIcon, QColor, QPixmap
from sqlalchemy.orm import Session
from database.models import Transaction, TransactionItem, Product, Supplier, TransactionType

class ProductSelectionDialog(QDialog):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.setWindowTitle("إضافة منتج للفاتورة")
        self.setFixedSize(500, 400)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # منطقة البحث عن المنتج
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث عن المنتج...")
        self.search_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
            }
        """)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # قائمة المنتجات
        self.products_list = QTableWidget()
        self.products_list.setColumnCount(4)
        self.products_list.setHorizontalHeaderLabels(["الكود", "المنتج", "السعر", "المتوفر"])
        self.products_list.horizontalHeader().setSectionResizeMode(1, QHeaderView.Stretch)
        layout.addWidget(self.products_list)
        
        # منطقة تحديد الكمية والسعر
        details_frame = QFrame()
        details_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        details_layout = QGridLayout()
        details_frame.setLayout(details_layout)
        
        self.quantity_spin = QSpinBox()
        self.quantity_spin.setMaximum(1000)
        self.price_spin = QDoubleSpinBox()
        self.price_spin.setMaximum(1000000)
        
        details_layout.addWidget(QLabel("الكمية:"), 0, 0)
        details_layout.addWidget(self.quantity_spin, 0, 1)
        details_layout.addWidget(QLabel("السعر:"), 0, 2)
        details_layout.addWidget(self.price_spin, 0, 3)
        
        layout.addWidget(details_frame)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        confirm_btn = QPushButton("إضافة للفاتورة")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #218838;
            }
        """)
        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
            QPushButton:hover {
                background-color: #5A6268;
            }
        """)
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(confirm_btn)
        buttons_layout.addWidget(cancel_btn)
        layout.addLayout(buttons_layout)
        
        # ربط الأحداث
        confirm_btn.clicked.connect(self.accept)
        cancel_btn.clicked.connect(self.reject)
        self.search_input.textChanged.connect(self.search_products)
        self.products_list.itemSelectionChanged.connect(self.on_product_selected)
        
        # تحميل المنتجات
        self.load_products()
        
    def load_products(self):
        with Session(self.engine) as session:
            products = session.query(Product).all()
            self.products_list.setRowCount(len(products))
            
            for row, product in enumerate(products):
                self.products_list.setItem(row, 0, QTableWidgetItem(str(product.id)))
                self.products_list.setItem(row, 1, QTableWidgetItem(product.name))
                self.products_list.setItem(row, 2, QTableWidgetItem(str(product.purchase_price)))
                self.products_list.setItem(row, 3, QTableWidgetItem(str(product.quantity)))
                
    def search_products(self):
        search_text = self.search_input.text().lower()
        for row in range(self.products_list.rowCount()):
            product_name = self.products_list.item(row, 1).text().lower()
            self.products_list.setRowHidden(row, search_text not in product_name)
            
    def on_product_selected(self):
        selected_rows = self.products_list.selectedItems()
        if selected_rows:
            row = selected_rows[0].row()
            price = float(self.products_list.item(row, 2).text())
            self.price_spin.setValue(price)
            
    def get_selected_product(self):
        if self.exec_() == QDialog.Accepted:
            selected_rows = self.products_list.selectedItems()
            if selected_rows:
                row = selected_rows[0].row()
                product_id = int(self.products_list.item(row, 0).text())
                return product_id, self.quantity_spin.value(), self.price_spin.value()
        return None, None, None

class PurchasesWidget(QWidget):
    def __init__(self, engine):
        super().__init__()
        self.engine = engine
        self.items = []
        self.setup_ui()
        
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        try:
            with Session(self.engine) as session:
                suppliers = session.query(Supplier).filter(Supplier.is_active == True).all()
                self.supplier_combo.clear()
                self.supplier_combo.addItem("اختر المورد", None)
                for supplier in suppliers:
                    self.supplier_combo.addItem(supplier.name, supplier.id)
        except Exception as e:
            print(f"Error loading suppliers: {e}")
            QMessageBox.warning(self, "تنبيه", "حدث خطأ أثناء تحميل قائمة الموردين")

    def setup_ui(self):
        layout = QVBoxLayout()
        self.setLayout(layout)
        
        # Initialize supplier combo box first
        self.supplier_combo = QComboBox()
        self.supplier_combo.setStyleSheet("""
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
            }
        """)
        
        # Initialize paid amount field
        self.paid_amount = QDoubleSpinBox()
        self.paid_amount.setMaximum(1000000)
        self.paid_amount.valueChanged.connect(self.update_total)
        
        # Initialize discount and tax fields
        self.discount_spinbox = QDoubleSpinBox()
        self.discount_spinbox.setMaximum(100)
        self.discount_spinbox.setSuffix(" %")
        self.discount_spinbox.valueChanged.connect(self.update_total)
        
        self.tax_spinbox = QDoubleSpinBox()
        self.tax_spinbox.setMaximum(100)
        self.tax_spinbox.setValue(15)
        self.tax_spinbox.setSuffix(" %")
        self.tax_spinbox.valueChanged.connect(self.update_total)
        
        # Load suppliers after initializing the combo box
        self.load_suppliers()
        
        # تحسين العنوان والمعلومات الأساسية
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0, stop:0 #2C3E50, stop:1 #3498DB);
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 10px;
            }
            QLabel { color: white; }
            QComboBox {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
                min-width: 250px;
            }
        """)
        header_layout = QGridLayout()
        header_frame.setLayout(header_layout)

        # إضافة شعار المشتريات
        purchases_icon = QLabel()
        purchases_icon_pixmap = QPixmap("images/purchases.png")
        if not purchases_icon_pixmap.isNull():
            purchases_icon.setPixmap(purchases_icon_pixmap.scaled(48, 48, Qt.KeepAspectRatio, Qt.SmoothTransformation))
        
        # تحسين عنوان الفاتورة
        invoice_label = QLabel("فاتورة مشتريات جديدة")
        invoice_label.setStyleSheet("font-size: 24px; font-weight: bold; color: white;")
        
        # إضافة رقم الفاتورة التلقائي
        self.invoice_number = QLabel()
        self.generate_invoice_number()
        self.invoice_number.setStyleSheet("font-size: 16px; color: rgba(255, 255, 255, 0.9);")
        
        # إضافة التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setStyleSheet("""
            QDateEdit {
                background-color: rgba(255, 255, 255, 0.9);
                padding: 8px;
                border: none;
                border-radius: 5px;
            }
        """)
        
        header_layout.addWidget(purchases_icon, 0, 0)
        header_layout.addWidget(invoice_label, 0, 1)
        header_layout.addWidget(self.invoice_number, 0, 2)
        header_layout.addWidget(QLabel("المورد:"), 1, 0)
        header_layout.addWidget(self.supplier_combo, 1, 1)
        header_layout.addWidget(QLabel("التاريخ:"), 1, 2)
        header_layout.addWidget(self.date_edit, 1, 3)
        
        layout.addWidget(header_frame)

        # إضافة شريط البحث السريع
        search_frame = QFrame()
        search_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border-radius: 5px;
                padding: 10px;
            }
        """)
        search_layout = QHBoxLayout()
        search_frame.setLayout(search_layout)
        
        self.quick_search = QLineEdit()
        self.quick_search.setPlaceholderText("البحث السريع عن المنتجات...")
        self.quick_search.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 1px solid #DEE2E6;
                border-radius: 4px;
                min-width: 300px;
            }
        """)
        self.quick_search.textChanged.connect(self.quick_product_search)
        
        self.scan_barcode = QPushButton("مسح الباركود")
        self.scan_barcode.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 120px;
            }
        """)
        self.scan_barcode.clicked.connect(self.handle_barcode_scan)
        
        search_layout.addWidget(self.quick_search)
        search_layout.addWidget(self.scan_barcode)
        search_layout.addStretch()
        
        layout.addWidget(search_frame)

        # تحسين جدول المنتجات
        self.products_table = QTableWidget()
        self.products_table.setStyleSheet("""
            QTableWidget {
                border: 1px solid #DEE2E6;
                border-radius: 5px;
                background-color: white;
            }
            QTableWidget::item {
                padding: 12px;
            }
            QHeaderView::section {
                background-color: #F8F9FA;
                padding: 12px;
                border: none;
                font-weight: bold;
            }
        """)
        self.products_table.setColumnCount(6)
        self.products_table.setHorizontalHeaderLabels([
            "المنتج", "الكمية", "السعر", "الإجمالي", "حذف", ""
        ])
        
        header = self.products_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)
        for i in [1, 2, 3, 4, 5]:
            header.setSectionResizeMode(i, QHeaderView.Fixed)
            header.resizeSection(i, 100)
        
        layout.addWidget(self.products_table)
        
        # تحسين ملخص الفاتورة
        summary_frame = QFrame()
        summary_frame.setStyleSheet("""
            QFrame {
                background-color: #F8F9FA;
                border: 1px solid #DEE2E6;
                border-radius: 10px;
                padding: 20px;
            }
            QLabel {
                font-size: 14px;
            }
            .total-label {
                font-size: 18px;
                font-weight: bold;
                color: #2C3E50;
            }
        """)
        
        summary_layout = QGridLayout()
        
        # المجاميع
        subtotal_label = QLabel("المجموع الفرعي:")
        self.subtotal_value = QLabel("0.00")
        
        discount_label = QLabel("الخصم:")
        self.discount_value = QLabel("0.00")
        
        tax_label = QLabel("الضريبة:")
        self.tax_value = QLabel("0.00")
        
        total_label = QLabel("الإجمالي النهائي:")
        total_label.setProperty("class", "total-label")
        self.total_value = QLabel("0.00")
        self.total_value.setProperty("class", "total-label")
        
        # طريقة الدفع
        self.payment_method = QComboBox()
        self.payment_method.addItems(["نقداً", "تحويل بنكي", "شيك"])
        
        summary_layout.addWidget(subtotal_label, 0, 0)
        summary_layout.addWidget(self.subtotal_value, 0, 1)
        summary_layout.addWidget(discount_label, 1, 0)
        summary_layout.addWidget(self.discount_value, 1, 1)
        summary_layout.addWidget(tax_label, 2, 0)
        summary_layout.addWidget(self.tax_value, 2, 1)
        summary_layout.addWidget(total_label, 3, 0)
        summary_layout.addWidget(self.total_value, 3, 1)
        summary_layout.addWidget(QLabel("طريقة الدفع:"), 0, 2)
        summary_layout.addWidget(self.payment_method, 0, 3)
        summary_layout.addWidget(QLabel("المدفوع:"), 1, 2)
        summary_layout.addWidget(self.paid_amount, 1, 3)
        
        summary_frame.setLayout(summary_layout)
        layout.addWidget(summary_frame)
        
        # أزرار التحكم النهائية
        buttons_layout = QHBoxLayout()
        
        save_print_btn = QPushButton("حفظ وطباعة")
        save_print_btn.setStyleSheet("""
            QPushButton {
                background-color: #28A745;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_print_btn.clicked.connect(self.save_and_print)
        
        save_btn = QPushButton("حفظ الفاتورة")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #007BFF;
                min-width: 140px;
                padding: 10px;
            }
        """)
        save_btn.clicked.connect(self.save_invoice)
        
        clear_btn = QPushButton("فاتورة جديدة")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C757D;
                min-width: 140px;
                padding: 10px;
            }
        """)
        clear_btn.clicked.connect(self.clear_invoice)
        
        buttons_layout.addWidget(clear_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(save_btn)
        buttons_layout.addWidget(save_print_btn)
        
        layout.addLayout(buttons_layout)

    def generate_invoice_number(self):
        with Session(self.engine) as session:
            last_invoice = session.query(Transaction).filter(
                Transaction.type == TransactionType.PURCHASE
            ).order_by(Transaction.id.desc()).first()
            
            if last_invoice:
                new_number = last_invoice.id + 1
            else:
                new_number = 1
                
            self.invoice_number.setText(f"رقم الفاتورة: {new_number:06d}")

    def quick_product_search(self):
        search_text = self.quick_search.text().lower()
        if len(search_text) >= 3:
            with Session(self.engine) as session:
                products = session.query(Product).filter(
                    Product.name.ilike(f"%{search_text}%")
                ).all()
                
                if len(products) == 1:
                    product = products[0]
                    self.add_product_to_invoice(product)
                    self.quick_search.clear()

    def handle_barcode_scan(self):
        # يمكن إضافة دعم لقارئ الباركود هنا
        pass

    def add_product_to_invoice(self, product):
        dialog = ProductSelectionDialog(self.engine)
        dialog.quantity_spin.setValue(1)
        dialog.price_spin.setValue(product.purchase_price)
        
        if dialog.exec_() == QDialog.Accepted:
            quantity = dialog.quantity_spin.value()
            price = dialog.price_spin.value()
            self.add_product_to_table(product, quantity, price)

    def add_product_to_table(self, product, quantity, price):
        row = self.products_table.rowCount()
        self.products_table.insertRow(row)
        
        # إضافة المنتج للجدول مع تنسيق خلفية الصف
        bg_color = QColor("#F8F9FA") if row % 2 == 0 else QColor("white")
        
        self.products_table.setItem(row, 0, self.create_table_item(product.name, bg_color))
        self.products_table.setItem(row, 1, self.create_table_item(str(quantity), bg_color))
        self.products_table.setItem(row, 2, self.create_table_item(str(price), bg_color))
        
        total = price * quantity
        total_item = self.create_table_item(f"{total:,.2f}", bg_color)
        total_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        self.products_table.setItem(row, 3, total_item)
        
        # زر الحذف
        delete_btn = QPushButton("حذف")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #DC3545;
                border: none;
                color: white;
                padding: 5px 10px;
            }
            QPushButton:hover {
                background-color: #C82333;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_product(row))
        self.products_table.setCellWidget(row, 4, delete_btn)
        
        self.items.append({
            'product_id': product.id,
            'quantity': quantity,
            'price': price
        })
        
        self.update_total()

    def create_table_item(self, text, bg_color):
        item = QTableWidgetItem(text)
        item.setBackground(bg_color)
        return item
                
    def remove_product(self, row):
        self.products_table.removeRow(row)
        self.items.pop(row)
        self.update_total()
        
    def update_total(self):
        subtotal = sum(item['price'] * item['quantity'] for item in self.items)
        discount = subtotal * (self.discount_spinbox.value() / 100)
        tax = (subtotal - discount) * (self.tax_spinbox.value() / 100)
        total = subtotal - discount + tax
        
        self.subtotal_value.setText(f"{subtotal:,.2f}")
        self.discount_value.setText(f"{discount:,.2f}")
        self.tax_value.setText(f"{tax:,.2f}")
        self.total_value.setText(f"{total:,.2f}")
        self.paid_amount.setMaximum(total)
        
    def save_invoice(self):
        if not self.items:
            QMessageBox.warning(self, "خطأ", "لا يوجد منتجات في الفاتورة")
            return

        supplier_id = self.supplier_combo.currentData()
        if not supplier_id:
            QMessageBox.warning(self, "خطأ", "يجب اختيار المورد")
            return
            
        total_amount = sum(item['price'] * item['quantity'] for item in self.items)
        paid_amount = self.paid_amount.value()
        
        try:
            with Session(self.engine) as session:
                # إنشاء الفاتورة
                transaction = Transaction(
                    type=TransactionType.PURCHASE,
                    total_amount=total_amount,
                    paid_amount=paid_amount,
                    supplier_id=supplier_id,
                    date=self.date_edit.date().toPython()
                )
                session.add(transaction)
                
                # إضافة المنتجات للفاتورة
                for item in self.items:
                    transaction_item = TransactionItem(
                        transaction=transaction,
                        product_id=item['product_id'],
                        quantity=item['quantity'],
                        price=item['price']
                    )
                    session.add(transaction_item)
                    
                    # تحديث المخزون
                    product = session.query(Product).get(item['product_id'])
                    product.quantity += item['quantity']
                    product.purchase_price = item['price']  # تحديث سعر الشراء
                    
                # تحديث رصيد المورد
                supplier = session.query(Supplier).get(supplier_id)
                supplier.balance += (total_amount - paid_amount)
                
                session.commit()
                
                QMessageBox.information(self, "نجاح", "تم حفظ الفاتورة بنجاح")
                self.clear_invoice()
                return True
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الفاتورة: {str(e)}")
            return False
            
    def save_and_print(self):
        if self.save_invoice():
            self.print_invoice()

    def print_invoice(self):
        # إضافة كود الطباعة هنا
        pass

    def clear_invoice(self):
        self.products_table.setRowCount(0)
        self.items.clear()
        self.paid_amount.setValue(0)
        self.discount_spinbox.setValue(0)
        self.quick_search.clear()
        self.generate_invoice_number()
        self.update_total()